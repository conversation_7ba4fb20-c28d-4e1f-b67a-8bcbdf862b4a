import React, { useEffect, useState } from 'react';
import { Form, Button, message, FormInstance } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import {
  CommonBaseDataSelector,
  BusTypeDropdownListSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { isOrNoMap, handleAttributeMap, handleObjectMap, handleMethodMap } from '../index';

interface BusinessContentFormProps {
  modal: [boolean, CallableFunction];
  listOptions: any;
  initialInfo?: any;
}

const BusinessContentForm: React.FC<BusinessContentFormProps> = ({
  modal,
  listOptions,
  initialInfo,
}) => {
  const [visible, setVisible] = modal;
  if (!visible) return null;

  const [form] = Form.useForm();
  const [businessTypeId, setBusinessTypeId] = useState<string>('');
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (visible) {
      if (initialInfo && Object.keys(initialInfo).length > 0) {
        setIsEdit(true);
        form.setFieldsValue(initialInfo);
        setBusinessTypeId(initialInfo.businessType);
      } else {
        setIsEdit(false);
        form.resetFields();
        setBusinessTypeId('');
      }
    }
  }, [visible, initialInfo, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      // TODO: 调用API保存数据
      console.log('业务内容提交:', values);
      message.success(isEdit ? '修改成功' : '新增成功');
      setVisible(false);
      form.resetFields();
      // 刷新列表
      listOptions.request(listOptions.queries);
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={() => setVisible(false)}>
      取消
    </Button>,
    <Button key="submit" type="primary" onClick={handleSubmit}>
      确认
    </Button>,
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '业务类型',
      fieldName: 'businessType',
      inputRender: (outerForm: FormInstance) => (
        <CommonBaseDataSelector
          allowClear
          params={{ type: '719' }}
          onConfirm={() => {
            outerForm.setFieldsValue({ businessProject: undefined });
          }}
        />
      ),
    },
    {
      label: '业务项目',
      fieldName: 'businessProject',
      inputRender: (outerForm: FormInstance) => (
        <BusTypeDropdownListSelector
          allowClear
          params={{ categoryId: outerForm.getFieldValue('businessType') ?? '' }}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.businessType !== curValues.businessType;
      },
    },
    {
      label: '业务内容',
      fieldName: 'businessContent',
      inputRender: 'string',
    },
    {
      label: '办理属性',
      fieldName: 'handleAttribute',
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'handleObject',
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
    },
    {
      label: '办理方式',
      fieldName: 'handleMethod',
      inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
    },
    {
      label: '是否微信显示',
      fieldName: 'isWechatShow',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: 'isClientShow',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
  ];

  // 设置只读字段
  const readOnlyFields = isEdit
    ? {
        handleAttribute: true,
        handleObject: true,
        handleMethod: true,
      }
    : undefined;

  return (
    <Codal
      title={isEdit ? '修改业务内容' : '新增业务内容'}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        setVisible(false);
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="60%"
    >
      <FormElement3 form={form}>
        <EnumerateFields
          formColumns={formColumns}
          outerForm={form}
          colNumber={2}
          readOnlyFields={readOnlyFields}
        />
      </FormElement3>
    </Codal>
  );
};

export default BusinessContentForm;
