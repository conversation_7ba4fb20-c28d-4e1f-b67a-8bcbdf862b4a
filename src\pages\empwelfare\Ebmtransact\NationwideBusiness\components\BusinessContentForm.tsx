import React, { useEffect, useState } from 'react';
import { Mo<PERSON>, Form, Row, Col, Button, message } from 'antd';
import { EditeForm } from '@/components/EditeForm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import { 
  CommonBaseDataSelector,
  BusTypeDropdownListSelector 
} from '@/components/Selectors/BaseDataSelectors';
import { 
  isOrNoMap, 
  handleAttributeMap, 
  handleObjectMap, 
  handleMethodMap 
} from '../index';

interface BusinessContentFormProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  initialValues?: any;
  title: string;
  loading?: boolean;
}

const BusinessContentForm: React.FC<BusinessContentFormProps> = ({
  visible,
  onCancel,
  onOk,
  initialValues,
  title,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const [businessTypeId, setBusinessTypeId] = useState<string>('');
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (visible) {
      if (initialValues) {
        setIsEdit(true);
        form.setFieldsValue(initialValues);
        setBusinessTypeId(initialValues.businessType);
      } else {
        setIsEdit(false);
        form.resetFields();
        setBusinessTypeId('');
      }
    }
  }, [visible, initialValues, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const formItems = [
    {
      label: '业务类型',
      fieldName: 'businessType',
      required: true,
      inputRender: () => (
        <CommonBaseDataSelector
          allowClear
          params={{ type: '719' }}
          onChange={(val: any) => {
            setBusinessTypeId(val);
            // 清空业务项目选择
            form.setFieldsValue({ businessProject: undefined });
          }}
        />
      ),
    },
    {
      label: '业务项目',
      fieldName: 'businessProject',
      required: true,
      inputRender: () => (
        <BusTypeDropdownListSelector
          allowClear
          params={{ categoryId: businessTypeId }}
        />
      ),
    },
    {
      label: '业务内容',
      fieldName: 'businessContent',
      required: true,
      inputRender: 'string',
    },
    {
      label: '办理属性',
      fieldName: 'handleAttribute',
      required: true,
      disabled: isEdit, // 修改时只读
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'handleObject',
      required: true,
      disabled: isEdit, // 修改时只读
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
    },
    {
      label: '办理方式',
      fieldName: 'handleMethod',
      required: true,
      disabled: isEdit, // 修改时只读
      inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
    },
    {
      label: '是否微信显示',
      fieldName: 'isWechatShow',
      required: true,
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: 'isClientShow',
      required: true,
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
  ];

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          确认
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical">
        <Row gutter={16}>
          {formItems.map((item, index) => (
            <Col span={12} key={item.fieldName}>
              <EditeForm
                {...item}
                form={form}
                colNumber={1}
                formOptions={{
                  rules: item.required ? [{ required: true, message: `请输入${item.label}` }] : [],
                }}
              />
            </Col>
          ))}
        </Row>
      </Form>
    </Modal>
  );
};

export default BusinessContentForm;
