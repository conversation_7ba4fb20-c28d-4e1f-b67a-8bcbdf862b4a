import React, { useEffect, useState } from 'react';
import { Form, Button, message } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import {
  CommonBaseDataSelector,
  BusTypeDropdownListSelector
} from '@/components/Selectors/BaseDataSelectors';
import {
  isOrNoMap,
  handleAttributeMap,
  handleObjectMap,
  handleMethodMap
} from '../index';

interface BusinessContentFormProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  initialValues?: any;
  title: string;
  loading?: boolean;
}

const BusinessContentForm: React.FC<BusinessContentFormProps> = ({
  visible,
  onCancel,
  onOk,
  initialValues,
  title,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const [businessTypeId, setBusinessTypeId] = useState<string>('');
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (visible) {
      if (initialValues) {
        setIsEdit(true);
        form.setFieldsValue(initialValues);
        setBusinessTypeId(initialValues.businessType);
      } else {
        setIsEdit(false);
        form.resetFields();
        setBusinessTypeId('');
      }
    }
  }, [visible, initialValues, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={onCancel}>
      取消
    </Button>,
    <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
      确认
    </Button>,
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '业务类型',
      fieldName: 'businessType',
      inputRender: () => (
        <CommonBaseDataSelector
          allowClear
          params={{ type: '719' }}
          onChange={(val: any) => {
            setBusinessTypeId(val);
            // 清空业务项目选择
            form.setFieldsValue({ businessProject: undefined });
          }}
        />
      ),
    },
    {
      label: '业务项目',
      fieldName: 'businessProject',
      inputRender: () => (
        <BusTypeDropdownListSelector
          allowClear
          params={{ categoryId: businessTypeId }}
        />
      ),
    },
    {
      label: '业务内容',
      fieldName: 'businessContent',
      inputRender: 'string',
    },
    {
      label: '办理属性',
      fieldName: 'handleAttribute',
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'handleObject',
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
    },
    {
      label: '办理方式',
      fieldName: 'handleMethod',
      inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
    },
    {
      label: '是否微信显示',
      fieldName: 'isWechatShow',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: 'isClientShow',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
  ];

  // 设置只读字段
  const readOnlyFields = isEdit ? {
    handleAttribute: true,
    handleObject: true,
    handleMethod: true,
  } : undefined;

  return (
    <Codal
      title={title}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        onCancel();
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="80%"
    >
      <FormElement3 form={form}>
        <EnumerateFields
          formColumns={formColumns}
          outerForm={form}
          colNumber={2}
          readOnlyFields={readOnlyFields}
        />
      </FormElement3>
    </Codal>
  );
};

export default BusinessContentForm;
