import React, { useEffect } from 'react';
import { Modal, Form, Button, message } from 'antd';
import { EditeForm } from '@/components/EditeForm';

interface BusinessNodeFormProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  initialValues?: any;
  title: string;
  loading?: boolean;
}

const BusinessNodeForm: React.FC<BusinessNodeFormProps> = ({
  visible,
  onCancel,
  onOk,
  initialValues,
  title,
  loading = false,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      if (initialValues) {
        form.setFieldsValue(initialValues);
      } else {
        form.resetFields();
      }
    }
  }, [visible, initialValues, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const formItems = [
    {
      label: '业务节点',
      fieldName: 'businessNodeName',
      required: true,
      inputRender: 'string',
    },
  ];

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      width={400}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          确认
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical">
        {formItems.map((item) => (
          <EditeForm
            key={item.fieldName}
            {...item}
            form={form}
            colNumber={1}
            formOptions={{
              rules: item.required ? [{ required: true, message: `请输入${item.label}` }] : [],
            }}
          />
        ))}
      </Form>
    </Modal>
  );
};

export default BusinessNodeForm;
