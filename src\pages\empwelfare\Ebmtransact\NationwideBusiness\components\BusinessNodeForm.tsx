import React, { useEffect } from 'react';
import { Form, Button, message } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';

interface BusinessNodeFormProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  initialValues?: any;
  title: string;
  loading?: boolean;
}

const BusinessNodeForm: React.FC<BusinessNodeFormProps> = ({
  visible,
  onCancel,
  onOk,
  initialValues,
  title,
  loading = false,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      if (initialValues) {
        form.setFieldsValue(initialValues);
      } else {
        form.resetFields();
      }
    }
  }, [visible, initialValues, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={onCancel}>
      取消
    </Button>,
    <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
      确认
    </Button>,
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '业务节点',
      fieldName: 'businessNodeName',
      inputRender: 'string',
    },
  ];

  return (
    <Codal
      title={title}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        onCancel();
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="50%"
    >
      <FormElement3 form={form}>
        <EnumerateFields
          formColumns={formColumns}
          outerForm={form}
          colNumber={1}
        />
      </FormElement3>
    </Codal>
  );
};

export default BusinessNodeForm;
