import React, { useState } from 'react';
import { Button, Space, Card } from 'antd';
import BusinessContentForm from './components/BusinessContentForm';
import BusinessNodeForm from './components/BusinessNodeForm';

/**
 * 演示页面 - 展示弹窗表单功能
 * 这个文件仅用于开发测试，不会在生产环境中使用
 */
const NationwideBusinessDemo: React.FC = () => {
  const [businessContentVisible, setBusinessContentVisible] = useState(false);
  const [businessNodeVisible, setBusinessNodeVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const mockBusinessContentData = {
    businessType: '719001',
    businessProject: 'project001',
    businessContent: '工伤认定申请',
    handleAttribute: '1',
    handleObject: '1',
    handleMethod: '1',
    isWechatShow: '1',
    isClientShow: '1',
  };

  const mockBusinessNodeData = {
    businessNodeName: '材料审核',
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    console.log('提交数据:', values);
    
    // 模拟API调用
    setTimeout(() => {
      setLoading(false);
      setBusinessContentVisible(false);
      setBusinessNodeVisible(false);
    }, 1000);
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="全国业务维护弹窗演示" style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            type="primary" 
            onClick={() => setBusinessContentVisible(true)}
          >
            新增业务内容
          </Button>
          <Button 
            onClick={() => {
              setBusinessContentVisible(true);
            }}
          >
            修改业务内容（带初始值）
          </Button>
          <Button 
            type="primary" 
            onClick={() => setBusinessNodeVisible(true)}
          >
            新增业务节点
          </Button>
          <Button 
            onClick={() => {
              setBusinessNodeVisible(true);
            }}
          >
            修改业务节点（带初始值）
          </Button>
        </Space>
      </Card>

      {/* 业务内容弹窗 */}
      <BusinessContentForm
        visible={businessContentVisible}
        onCancel={() => setBusinessContentVisible(false)}
        onOk={handleSubmit}
        initialValues={mockBusinessContentData}
        title="业务内容维护"
        loading={loading}
      />

      {/* 业务节点弹窗 */}
      <BusinessNodeForm
        visible={businessNodeVisible}
        onCancel={() => setBusinessNodeVisible(false)}
        onOk={handleSubmit}
        initialValues={mockBusinessNodeData}
        title="业务节点维护"
        loading={loading}
      />
    </div>
  );
};

export default NationwideBusinessDemo;
