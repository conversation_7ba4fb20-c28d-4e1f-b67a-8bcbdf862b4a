# 全国业务维护页面

## 功能说明

本页面实现了全国业务维护功能，包括业务内容维护和业务节点维护两个部分。

### 业务内容维护

#### 查询条件
- **业务类型**：下拉选项（社保业务、公积金业务、人力资源收费业务）
- **业务项目**：下拉选项，与业务类型联动（工伤业务、生育业务、失业业务、医疗业务、公积金提取、退休业务、员工证明开具等）
- **业务内容**：输入项，支持模糊查询
- **办理属性**：下拉选项（流程业务、单次业务）
- **办理对象**：下拉选项（客户、员工）
- **办理方式**：下拉选项（员工办理、客户办理）
- **是否微信显示**：下拉选项（是、否）
- **是否客户端显示**：下拉选项（是、否）

#### 查询结果列表
显示以下字段：
- 业务内容编号
- 业务类型
- 业务项目
- 业务内容
- 办理属性
- 办理对象
- 办理方式
- 是否微信显示
- 是否客户端显示
- 是否被引用

#### 操作按钮
- **查询**：按照查询条件进行数据查询
- **新增**：新增业务内容（需要权限）
- **修改**：修改业务内容（需要权限，未被引用的可修改）
- **删除**：删除业务内容（需要权限，未被引用的可删除）
- **发布**：将记录状态改为"生效"
- **失效**：将记录状态改为"失效"
- **导出数据**：导出查询结果（需要权限）

### 业务节点维护

#### 查询结果列表
显示以下字段：
- 业务节点编号
- 业务节点
- 是否被引用

#### 操作按钮
- **新增**：新增业务节点（需要权限）
- **修改**：修改业务节点（需要权限，未被引用的可修改）
- **删除**：删除业务节点（需要权限，未被引用的可删除）

## 技术实现

### 组件结构
- 使用 `CachedPage` 组件实现分页查询
- 使用 `CommonBaseDataSelector` 和 `BusTypeDropdownListSelector` 实现下拉选择
- 使用 `AuthButtons` 实现权限控制

### API接口
- 查询数据：`API.welfaremanage.ebmtransact.getData`
- 导出数据：`API.welfaremanage.ebmtransact.exportExcel`

### 权限控制
- `nationwide_business_add`：新增业务内容
- `nationwide_business_edit`：修改业务内容
- `nationwide_business_delete`：删除业务内容
- `nationwide_business_publish`：发布业务内容
- `nationwide_business_invalid`：失效业务内容
- `nationwide_business_export`：导出业务内容
- `nationwide_business_node_add`：新增业务节点
- `nationwide_business_node_edit`：修改业务节点
- `nationwide_business_node_delete`：删除业务节点

## 数据字典

### 办理属性
- 1：流程业务
- 2：单次业务

### 办理对象
- 1：客户
- 2：员工

### 办理方式
- 1：员工办理
- 2：客户办理

### 是否选项
- 1：是
- 0：否
