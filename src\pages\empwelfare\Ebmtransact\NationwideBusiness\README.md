# 全国业务维护页面

## 功能说明

本页面实现了全国业务维护功能，包括业务内容维护和业务节点维护两个部分。

### 业务内容维护

#### 查询条件
- **业务类型**：下拉选项（社保业务、公积金业务、人力资源收费业务）
- **业务项目**：下拉选项，与业务类型联动（工伤业务、生育业务、失业业务、医疗业务、公积金提取、退休业务、员工证明开具等）
- **业务内容**：输入项，支持模糊查询
- **办理属性**：下拉选项（流程业务、单次业务）
- **办理对象**：下拉选项（客户、员工）
- **办理方式**：下拉选项（员工办理、客户办理）
- **是否微信显示**：下拉选项（是、否）
- **是否客户端显示**：下拉选项（是、否）

#### 查询结果列表
显示以下字段：
- 业务内容编号
- 业务类型
- 业务项目
- 业务内容
- 办理属性
- 办理对象
- 办理方式
- 是否微信显示
- 是否客户端显示
- 是否被引用

#### 操作按钮
- **查询**：按照查询条件进行数据查询
- **新增**：新增业务内容（需要权限）
- **修改**：修改业务内容（需要权限，未被引用的可修改）
- **删除**：删除业务内容（需要权限，未被引用的可删除）
- **发布**：将记录状态改为"生效"
- **失效**：将记录状态改为"失效"
- **导出数据**：导出查询结果（需要权限）

### 业务节点维护

#### 查询结果列表
显示以下字段：
- 业务节点编号
- 业务节点
- 是否被引用

#### 操作按钮
- **新增**：新增业务节点（需要权限）
- **修改**：修改业务节点（需要权限，未被引用的可修改）
- **删除**：删除业务节点（需要权限，未被引用的可删除）

## 技术实现

### 组件结构
- 使用 `CachedPage` 组件实现分页查询
- 使用 `CommonBaseDataSelector` 和 `BusTypeDropdownListSelector` 实现下拉选择
- 使用 `AuthButtons` 实现权限控制
- 使用 `BusinessContentForm` 组件实现业务内容的新增/修改弹窗
- 使用 `BusinessNodeForm` 组件实现业务节点的新增/修改弹窗

### 弹窗技术架构
#### Codal 组件
- 统一的弹窗容器组件，替代 Antd 的 Modal
- 支持自定义 footer 按钮
- 支持 checkEdit 属性控制编辑状态
- 宽度可配置（业务内容80%，业务节点50%）

#### FormElement3 组件
- 三列表单布局容器
- 与 Form 实例绑定
- 提供统一的表单样式

#### EnumerateFields 组件
- 动态表单字段渲染器
- 根据 formColumns 配置自动生成表单项
- 支持 colNumber 控制列数
- 支持 readOnlyFields 控制字段只读状态
- 支持复杂的输入组件渲染

### 集成模式说明
#### 参考 AddThreeInOneIntegration 模式
```tsx
// 在按钮渲染函数中直接集成弹窗组件
const renderButtons = (options: WritableInstance) => (
  <>
    <Button onClick={() => setVisible(true)}>新增</Button>
    <Button onClick={() => handleEdit(options)}>修改</Button>

    {/* 弹窗组件直接集成在这里 */}
    <BusinessContentForm
      modal={[visible, setVisible]}
      listOptions={options}
      initialInfo={initialInfo}
    />
  </>
);
```

#### 优势
1. **状态管理简化**：弹窗状态由组件内部管理
2. **自动刷新**：提交成功后自动刷新列表数据
3. **代码集中**：按钮和弹窗逻辑集中在一起，便于维护
4. **复用性强**：组件可以在不同页面中复用
5. **架构统一**：与项目其他页面保持一致的实现模式

### 弹窗功能
#### 业务内容弹窗 (`BusinessContentForm`)
- **组件架构**：使用 `Codal` + `FormElement3` + `EnumerateFields` 实现
- **集成方式**：直接集成在 `renderButtons` 函数中，参考 `AddThreeInOneIntegration` 模式
- **接口设计**：
  - `modal: [boolean, CallableFunction]` - 弹窗显示状态和控制函数
  - `listOptions: any` - 列表操作实例，用于刷新数据
  - `initialInfo?: any` - 初始数据，用于区分新增/修改模式
- **布局**：2列表单布局，宽度80%
- **功能特性**：
  - 支持新增和修改两种模式
  - 修改模式下，办理属性、办理对象、办理方式字段为只读
  - 业务类型和业务项目支持联动选择
  - 业务类型变更时自动清空业务项目选择
  - 表单验证：所有字段均为必填项
  - 自动处理表单提交和列表刷新

#### 业务节点弹窗 (`BusinessNodeForm`)
- **组件架构**：使用 `Codal` + `FormElement3` + `EnumerateFields` 实现
- **集成方式**：直接集成在 `renderBusinessNodeButtons` 函数中
- **接口设计**：与业务内容弹窗相同
- **布局**：1列表单布局，宽度50%
- **功能特性**：
  - 支持新增和修改两种模式
  - 简单的业务节点名称输入
  - 表单验证：业务节点名称为必填项
  - 自动处理表单提交和列表刷新

### API接口
- 查询数据：`API.welfaremanage.ebmtransact.getData`
- 导出数据：`API.welfaremanage.ebmtransact.exportExcel`
- 新增/修改业务内容：待实现
- 新增/修改业务节点：待实现

### 权限控制
- `nationwide_business_add`：新增业务内容
- `nationwide_business_edit`：修改业务内容
- `nationwide_business_delete`：删除业务内容
- `nationwide_business_publish`：发布业务内容
- `nationwide_business_invalid`：失效业务内容
- `nationwide_business_export`：导出业务内容
- `nationwide_business_node_add`：新增业务节点
- `nationwide_business_node_edit`：修改业务节点
- `nationwide_business_node_delete`：删除业务节点

## 数据字典

### 办理属性
- 1：流程业务
- 2：单次业务

### 办理对象
- 1：客户
- 2：员工

### 办理方式
- 1：员工办理
- 2：客户办理

### 是否选项
- 1：是
- 0：否

## 使用说明

### 新增业务内容
1. 点击"新增"按钮
2. 在弹出的表单中填写所有必填字段：
   - 业务类型：选择业务所属的大类
   - 业务项目：根据业务类型联动显示可选项目
   - 业务内容：输入具体的业务内容名称
   - 办理属性：选择流程业务或单次业务
   - 办理对象：选择客户或员工
   - 办理方式：选择员工办理或客户办理
   - 是否微信显示：选择是否在微信端显示
   - 是否客户端显示：选择是否在客户端显示
3. 点击"确认"按钮保存

### 修改业务内容
1. 在列表中选择要修改的记录（单选）
2. 点击"修改"按钮
3. 在弹出的表单中修改相关字段
   - 注意：办理属性、办理对象、办理方式在修改模式下为只读
4. 点击"确认"按钮保存

### 新增业务节点
1. 在业务节点维护区域点击"新增"按钮
2. 输入业务节点名称
3. 点击"确认"按钮保存

### 修改业务节点
1. 在业务节点列表中选择要修改的记录（单选）
2. 点击"修改"按钮
3. 修改业务节点名称
4. 点击"确认"按钮保存

### 注意事项
- 业务内容名称不能重复，如重复会提示"当前业务内容已存在，无需新建"
- 只有未被引用的业务内容和业务节点才能进行修改和删除
- 修改业务类型、业务项目、业务内容后，被引用的城市级、客户级、员工级历史数据会同步更新
- 修改是否微信显示、是否客户端显示后，被引用的历史数据不会同步更新
