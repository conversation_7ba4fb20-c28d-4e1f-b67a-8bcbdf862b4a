import React, { useEffect, useState } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { Button, Form, Typography } from 'antd';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance } from '@/components/Writable';
import { AsyncButton } from '@/components/Forms/Confirm';
import { stdMonthFormatMoDash } from '@/utils/methods/times';
import { DateRange } from '@/components/DateRange4';
import { mapToSelectors, mapToSelectView } from '@/components/Selectors/FuncSelectors';
import { invoiceStatusType, verifyStatusType } from '@/utils/settings/finance/queryExBill';
import { AuthButtons } from '@/components/Forms/RenderButtons';

export const isOrgin = new Map<number | string, string>([
  ['1', '是'],
  ['0', '否'],
]);

const NationwideBusiness = () => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [userListMap, setUserListMap] = useState<Map<number, string>>(new Map());
  const [currentRow, setCurrentRow] = useState<Record<any, any>>({});
  const handleReceivableAmountClick = (record: any) => {
    setCurrentRow(record);
    setVisible(true);
  };

  useEffect(() => {
    const getUserList = async () => {
      const params = {
        statementName: 'cc.getUserList',
      };
      const data = await API.basedata.baseDataCls.getDorpDownList.requests(params, { params });
      const map = new Map<number, string>();
      data.list.forEach((e: any) => map.set(+e.key, e.shortName));
      setUserListMap(map);
    };
    getUserList();
  }, []);

  const formColumns: EditeFormProps[] = [
    {
      label: '业务类型',
      fieldName: '10',
    },
    {
      label: '业务项目',
      fieldName: '11',
    },
    {
      label: '业务内容',
      fieldName: '12',
      inputRender: 'string',
    },
    {
      label: '办理属性',
      fieldName: '13',
    },
    {
      label: '办理对象',
      fieldName: '14',
    },
    {
      label: '办理方式',
      fieldName: 'createByName',
    },
    {
      label: '是否微信显示',
      fieldName: 'verifyStatus',
      inputRender: () => mapToSelectors(verifyStatusType, { allowClear: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: '1222',
      inputRender: () => mapToSelectors(invoiceStatusType, { allowClear: true }),
    },
  ];

  const columns: WritableColumnProps<any>[] = [
    { title: '业务内容编号', dataIndex: 'customerCode' },
    { title: '业务类型', dataIndex: 'custName' },
    { title: '业务项目', dataIndex: 'receivableTempltName' },
    { title: '业务内容', dataIndex: 'signBranchTitleName' },
    { title: '办理属性', dataIndex: 'billYm' },
    { title: '办理对象', dataIndex: 'finReceivableYm' },
    { title: '办理方式', dataIndex: 'createDt' },
    { title: '是否微信显示', dataIndex: 'VERIFYTYPENAME' },
    { title: '是否客户端显示', dataIndex: 'VERIFYTYPENAME' },
    { title: '是否被引用', dataIndex: 'createByName' },
  ];

  const columns2: WritableColumnProps<any>[] = [
    { title: '业务节点编号', dataIndex: 'customerCode' },
    { title: '业务节点', dataIndex: 'custName' },
    { title: '是否被引用', dataIndex: 'receivableTempltName' },
  ];

  const renderButtons = (options: WritableInstance) => (
    <>
      <Button type="primary" htmlType="submit">
        查询
      </Button>
      <AuthButtons funcId="materials_add">
        <Button>新增</Button>
      </AuthButtons>
      <AuthButtons funcId="materials_add">
        <Button>修改</Button>
      </AuthButtons>
      <AuthButtons funcId="materials_add">
        <AsyncButton>删除</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="materials_add">
        <AsyncButton>发布</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="materials_add">
        <AsyncButton>失效</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="materials_add">
        <AsyncButton
          onClick={() => {
            options.handleExport(
              { service: API.emphiresep.ciReceivable.exportFile },
              {
                columns,
                condition: { ...options.queries },
                fileName: '商保应收查询.xlsx',
              },
            );
          }}
        >
          导出
        </AsyncButton>
      </AuthButtons>
    </>
  );

  return (
    <>
      <CachedPage
        service={API.emphiresep.ciReceivable.queryCiReceivablePage}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        form={form}
        // handleQueries={(queries) => {
        //   return queries;
        // }}
        notShowRowSelection
      />
    </>
  );
};

export default NationwideBusiness;
