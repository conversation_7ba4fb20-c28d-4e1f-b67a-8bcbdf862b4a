import React, { useState } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { Button, Form, FormInstance, Typography } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance } from '@/components/Writable';
import { AsyncButton } from '@/components/Forms/Confirm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import {
  CommonBaseDataSelector,
  BusTypeDropdownListSelector,
} from '@/components/Selectors/BaseDataSelectors';

export const isOrNoMap = new Map<number | string, string>([
  ['1', '是'],
  ['0', '否'],
]);

export const handleAttributeMap = new Map<number | string, string>([
  ['1', '流程业务'],
  ['2', '单次业务'],
]);

export const handleObjectMap = new Map<number | string, string>([
  ['1', '客户'],
  ['2', '员工'],
]);

export const handleMethodMap = new Map<number | string, string>([
  ['1', '员工办理'],
  ['2', '客户办理'],
]);

const NationwideBusiness = () => {
  const [form] = Form.useForm();
  const formColumns: EditeFormProps[] = [
    {
      label: '业务类型',
      fieldName: 'businessType',
      inputRender: () => (
        <CommonBaseDataSelector
          allowClear
          params={{ type: '719' }}
        />
      ),
    },
    {
      label: '业务项目',
      fieldName: 'businessProject',
      inputRender: (outerForm: FormInstance) => (
        <BusTypeDropdownListSelector allowClear params={{ categoryId: outerForm.getFieldValue('businessType') ?? '' }} />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.businessType !== curValues.businessType;
      },
    },
    {
      label: '业务内容',
      fieldName: 'businessContent',
      inputRender: 'string',
    },
    {
      label: '办理属性',
      fieldName: 'handleAttribute',
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'handleObject',
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
    },
    {
      label: '办理方式',
      fieldName: 'handleMethod',
      inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
    },
    {
      label: '是否微信显示',
      fieldName: 'isWechatShow',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: 'isClientShow',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
  ];

  const columns: WritableColumnProps<any>[] = [
    { title: '业务内容编号', dataIndex: 'businessContentId' },
    { title: '业务类型', dataIndex: 'businessTypeName' },
    { title: '业务项目', dataIndex: 'businessProjectName' },
    { title: '业务内容', dataIndex: 'businessContentName' },
    { title: '办理属性', dataIndex: 'handleAttributeName' },
    { title: '办理对象', dataIndex: 'handleObjectName' },
    { title: '办理方式', dataIndex: 'handleMethodName' },
    {
      title: '是否微信显示',
      dataIndex: 'isWechatShow',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    {
      title: '是否客户端显示',
      dataIndex: 'isClientShow',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    {
      title: '是否被引用',
      dataIndex: 'isReferenced',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
  ];

  const businessNodeColumns: WritableColumnProps<any>[] = [
    { title: '业务节点编号', dataIndex: 'businessNodeId' },
    { title: '业务节点', dataIndex: 'businessNodeName' },
    {
      title: '是否被引用',
      dataIndex: 'isReferenced',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
  ];

  const renderButtons = (options: WritableInstance) => (
    <>
      <Button type="primary" htmlType="submit">
        查询
      </Button>
      <AuthButtons funcId="nationwide_business_btn">
        <Button>新增</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button>修改</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton>删除</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton>发布</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton>失效</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton
          onClick={() => {
            options.handleExport(
              { service: API.welfaremanage.ebmtransact.exportExcel },
              {
                columns,
                condition: { ...options.queries },
                fileName: '全国业务维护.xlsx',
              },
            );
          }}
        >
          导出数据
        </AsyncButton>
      </AuthButtons>
    </>
  );

  const renderBusinessNodeButtons = (options: WritableInstance) => (
    <>
      <AuthButtons funcId="nationwide_business_btn">
        <Button>新增</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button>修改</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton>删除</AsyncButton>
      </AuthButtons>
    </>
  );

  return (
    <>
      <CachedPage
        service={API.welfaremanage.ebmtransact.getData}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        form={form}
        notShowRowSelection
      />
      <CachedPage
        service={API.welfaremanage.ebmtransact.getData}
        formColumns={[]}
        columns={businessNodeColumns}
        renderButtons={renderBusinessNodeButtons}
        notShowRowSelection
      />
    </>
  );
};

export default NationwideBusiness;
